{"name": "merge", "author": "<PERSON><PERSON><PERSON>", "version": "5.4.0", "description": "<PERSON><PERSON>", "private": true, "packageManager": "yarn@4.9.2", "scripts": {"dev": "next dev -p 8081", "start": "NODE_OPTIONS=\"--max-old-space-size=7168\" next start -p 8000", "build": "next build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "clear-all": "rm -rf node_modules .next out dist build", "re-start": "rm -rf node_modules .next out dist build && yarn install && yarn dev", "re-build": "rm -rf node_modules .next out dist build && yarn install && yarn build", "re-build-test": "rm -rf node_modules .next out dist build && npm install && npm run build", "analyze": "cross-env ANALYZE=true next build", "analyze:server": "cross-env BUNDLE_ANALYZE=server next build", "analyze:browser": "cross-env BUNDLE_ANALYZE=browser next build"}, "dependencies": {"@dagrejs/dagre": "^1.0.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.10.5", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/timeline": "^6.1.15", "@hello-pangea/dnd": "^17.0.0", "@hookform/resolvers": "^3.2.0", "@iconify/react": "^4.1.1", "@mui/base": "^5.0.0-beta.10", "@mui/icons-material": "^5.14.3", "@mui/lab": "^5.0.0-alpha.139", "@mui/material": "^5.14.5", "@mui/system": "^5.14.4", "@mui/x-data-grid": "^6.11.0", "@mui/x-date-pickers": "^6.11.0", "@next/third-parties": "^14.2.5", "@tanstack/react-query": "^4.32.6", "@tanstack/react-query-devtools": "^4.32.6", "@types/react-dom": "^18.3.0", "@uiw/react-json-view": "^2.0.0-alpha.27", "apexcharts": "^3.41.1", "autosuggest-highlight": "^3.3.4", "axios": "^1.4.0", "d3-selection": "^3.0.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "eslint-config-next": "14.2.5", "framer-motion": "^10.15.1", "highlight.js": "^11.8.0", "immer": "^10.1.1", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "minimal-shared": "^1.0.5", "mui-one-time-password-input": "^2.0.0", "next": "^14.2.5", "notistack": "^3.0.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "object-to-formdata": "^4.5.1", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-audio-voice-recorder": "^2.2.0", "react-color": "^2.19.3", "react-csv-downloader": "^2.9.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.45.4", "react-input-mask": "^2.0.4", "react-lazy-load-image-component": "^1.6.0", "react-markdown": "^8.0.7", "react-number-format": "^5.3.0", "react-organizational-chart": "^2.2.1", "react-phone-number-input": "3.4.12", "react-qr-code": "^2.0.15", "react-quill": "^2.0.0", "react-slick": "^0.29.0", "react-virtualized": "^9.22.5", "reactflow": "^11.9.4", "rehype-highlight": "^6.0.0", "rehype-raw": "^6.1.1", "remark-breaks": "^4.0.0", "remark-gfm": "^3.0.1", "simplebar-react": "^3.2.4", "slick-carousel": "^1.8.1", "socket.io-client": "^4.4.1", "swr": "^2.3.0", "utf-8-validate": "^6.0.3", "yet-another-react-lightbox": "^3.12.0", "yup": "^1.2.0", "zod": "^3.24.1", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/plugin-syntax-flow": "^7.22.5", "@babel/plugin-transform-react-jsx": "^7.22.5", "@next/bundle-analyzer": "^13.5.4", "@svgr/webpack": "^8.0.1", "@types/autosuggest-highlight": "^3.2.0", "@types/lodash": "^4.14.196", "@types/mapbox-gl": "^2.7.13", "@types/node": "^20.4.8", "@types/nprogress": "^0.2.0", "@types/numeral": "^2.0.2", "@types/react": "^18.3.3", "@types/react-color": "^3.0.6", "@types/react-input-mask": "^3.0.5", "@types/react-lazy-load-image-component": "^1.5.3", "@types/react-slick": "^0.23.10", "@types/react-virtualized": "^9.21.27", "@typescript-eslint/eslint-plugin": "^6.3.0", "@typescript-eslint/parser": "^6.3.0", "cross-env": "^7.0.3", "eslint": "^8.46.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.0.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.28.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "^3.0.1", "typescript": "^5.1.6"}}