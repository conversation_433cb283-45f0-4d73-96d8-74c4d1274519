import { axiosInstance } from 'src/utils/axios';
import { IBlingPaymentMethod } from 'src/types/integrations/bling/payment-method';

namespace BlingPaymentMethodRepositoryTypes {
  export type getPaymentMethods = {
    params: {
      companyId: number;
      integrationId: number;
    };
  };
}

export class BlingPaymentMethodRepository {
  static async getPaymentMethods({ params }: BlingPaymentMethodRepositoryTypes.getPaymentMethods) {
    const { data } = await axiosInstance.get<IBlingPaymentMethod[]>(
      `/companies/${params.companyId}/integrations/${params.integrationId}/bling/payment-methods`,
    );

    return data;
  }
}
