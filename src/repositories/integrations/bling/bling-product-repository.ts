import { axiosInstance } from 'src/utils/axios';
import { IPagination } from 'src/types/pagination';
import { IBlingProduct } from 'src/types/integrations/bling/product';

namespace BlingProductRepositoryTypes {
  export type getProducts = {
    params: {
      companyId: number;
      integrationId: number;
    };
    query: {
      page?: number;
      limit?: number;
      nome?: string;
    };
  };
}

export class BlingProductRepository {
  static async getProducts({ params, query }: BlingProductRepositoryTypes.getProducts) {
    const { data } = await axiosInstance.get<IPagination<IBlingProduct[]>>(
      `/companies/${params.companyId}/integrations/${params.integrationId}/bling/products`,
      {
        params: query,
      },
    );

    return data;
  }
}
