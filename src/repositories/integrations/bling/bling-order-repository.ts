import { IBlingOrderCreate, IBlingOrderDetail } from 'src/types/integrations/bling/order';
import { BlingStatus } from 'src/types/integrations/bling/status';
import { IPagination } from 'src/types/pagination';
import { axiosInstance } from 'src/utils/axios';

export type BlingOrderFindManyParams = {
  orderCode?: string;
  clientName?: string;
  status?: number[];
  carriers?: string[];
  startDate?: Date;
  endDate?: Date;
  shopIds?: string[];
  email?: string;
  document?: string;
  deliveryForecastStartDate?: Date;
  deliveryForecastEndDate?: Date;
  sellers?: number[];
};

export class BlingOrderRepository {
  static async getOrders(
    companyId: number,
    integrationId: number,
    page: number,
    limit: number,
    queries: BlingOrderFindManyParams,
  ) {
    const { data } = await axiosInstance.get<
      IPagination<{ id: string }[], { totalValue: number; averageDaysDifference: number }>
    >(`/companies/${companyId}/integrations/${integrationId}/bling/orders`, {
      params: {
        page,
        limit,
        ...queries,
      },
    });

    return data;
  }

  static async getOrdersTotalValue(
    companyId: number,
    integrationId: number,
    queries: BlingOrderFindManyParams,
  ) {
    const { data } = await axiosInstance.get<{ totalValue: number }>(
      `/companies/${companyId}/integrations/${integrationId}/bling/orders/total-value`,
      {
        params: {
          ...queries,
        },
      },
    );

    return data.totalValue;
  }

  static async getOrderByIds(companyId: number, integrationId: number, orderIds: string[]) {
    const { data } = await axiosInstance.get<IBlingOrderDetail[]>(
      `/companies/${companyId}/integrations/${integrationId}/bling/orders/details`,
      {
        params: {
          ids: orderIds,
        },
      },
    );

    return data;
  }

  static async show(companyId: number, integrationId: number, orderId: string) {
    const { data } = await axiosInstance.get<IBlingOrderDetail>(
      `/companies/${companyId}/integrations/${integrationId}/bling/orders/${orderId}`,
    );

    return data;
  }

  static async getAllContactOrders(companyId: number, integrationId: number, contactId: number) {
    const { data } = await axiosInstance.get<IBlingOrderDetail[]>(
      `/companies/${companyId}/integrations/${integrationId}/bling/orders/contact-orders/${contactId}`,
    );

    return data;
  }

  static async statuses(companyId: number, integrationId: number) {
    const { data } = await axiosInstance.get<BlingStatus[]>(
      `/companies/${companyId}/integrations/${integrationId}/bling/orders/filters/statuses`,
    );

    return data;
  }

  static async carriers(companyId: number, integrationId: number) {
    const { data } = await axiosInstance.get<string[]>(
      `/companies/${companyId}/integrations/${integrationId}/bling/orders/filters/carriers`,
    );

    return data;
  }

  static async create(companyId: number, integrationId: number, orderData: IBlingOrderCreate) {
    const { data } = await axiosInstance.post<IBlingOrderCreate>(
      `/companies/${companyId}/integrations/${integrationId}/bling/orders`,
      { pedidoData: orderData },
    );

    return data;
  }
}
