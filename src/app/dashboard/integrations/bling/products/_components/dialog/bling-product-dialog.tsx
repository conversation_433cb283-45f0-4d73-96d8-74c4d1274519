import { ListItemText } from '@mui/material';
import Button from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Stack from '@mui/material/Stack';
import { IBlingProduct } from 'src/types/integrations/bling/product';
import BlingProductGridDetails from '../grid/bling-product-grid-details';

// --------------------------------------------------------

type ComponentProps = {
  open: boolean;
  onClose: (shouldCloseQuickSearch?: boolean) => void;
  blingProduct: IBlingProduct;
} & DialogProps;

export default function BlingProductDialog({
  open,
  blingProduct,
  onClose,
  ...others
}: ComponentProps) {
  const renderContent = (
    <Stack spacing={2}>
      <Stack spacing={1}>
        <BlingProductGridDetails blingProduct={blingProduct} />
      </Stack>
    </Stack>
  );

  return (
    <Dialog fullWidth maxWidth="xl" open={open} onClose={onClose} {...others}>
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <ListItemText
            primary={'Detalhes do Produto'}
            primaryTypographyProps={{
              typography: 'h6',
            }}
          />
        </Stack>
      </DialogTitle>
      <DialogContent>{renderContent}</DialogContent>
      <DialogActions>
        <Button onClick={() => onClose(false)} variant="contained">
          Fechar
        </Button>
      </DialogActions>
    </Dialog>
  );
}
