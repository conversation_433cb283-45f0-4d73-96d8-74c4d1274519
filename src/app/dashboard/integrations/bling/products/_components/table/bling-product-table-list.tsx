import {
  alpha,
  Card,
  Container,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableContainer,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { isEqual } from 'lodash';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSnackbar } from 'notistack';
import { useCallback, useEffect, useState } from 'react';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import {
  TableHeadCustom,
  TableNoData,
  TablePaginationCustom,
  TableSkeleton,
  useTable,
} from 'src/components/table';
import { useAuthContext } from 'src/contexts/auth/hooks';
import { useBlingContext } from 'src/contexts/integrations/bling/bling-context';
import { useDebounce } from 'src/hooks/use-debounce';
import { BlingProductRepository } from 'src/repositories/integrations/bling/bling-product-repository';
import { paths } from 'src/routes/paths';
import { AppError } from 'src/utils/AppError';
import BlingProductTableRow from './bling-product-table-row';
import BlingProductTableToolbar from './bling-product-table-toolbar';

// ----------------------------------------------------------------------

const defaultFilters = {
  query: '',
  field: 'name',
};

const TABLE_HEAD = [
  { id: 'code', label: 'Código', align: 'left' },
  { id: 'name', label: 'Nome do Produto', align: 'left' },
  { id: 'price', label: 'Preço', align: 'right' },
  { id: 'dimensions', label: 'Dimensões (L x A x P)', align: 'center' },
  { id: 'type', label: 'Tipo', align: 'center' },
  { id: 'status', label: 'Status', align: 'center' },
  { id: 'actions', label: 'Ações', align: 'center' },
];

// ----------------------------------------------------------------------

export function BlingProductTableList() {
  const { user } = useAuthContext();
  const { selectedIntegrationId, setIntegrationId, blingIntegrations } = useBlingContext();
  const { push } = useRouter();
  const searchParams = useSearchParams();
  const { enqueueSnackbar } = useSnackbar();

  const searchPage = searchParams.get('page') ? Number(searchParams.get('page')) : 0;
  const pageSize = searchParams.get('pageSize') ? Number(searchParams.get('pageSize')) : 25;

  const {
    dense,
    page,
    order,
    orderBy,
    rowsPerPage,
    setPage,
    onChangeDense,
    onChangePage,
    onChangeRowsPerPage,
    onResetPage,
  } = useTable({
    defaultCurrentPage: searchPage,
    defaultRowsPerPage: pageSize,
  });

  const [filters, setFilters] = useState({
    ...defaultFilters,
    query: searchParams.get('query') ?? '',
  });

  const canReset = !isEqual(defaultFilters, filters);

  const handleFilters = useCallback(
    (name: string, value: string | string[]) => {
      onResetPage();
      setFilters((prev) => ({
        ...prev,
        [name]: Array.isArray(value) ? value : name === 'status' ? [value] : value,
      }));
    },
    [onResetPage],
  );

  const handleResetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  const debouncedFilters = useDebounce(filters, 500);

  const {
    data: products,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['bling-products', page + 1, rowsPerPage, debouncedFilters],
    queryFn: () =>
      BlingProductRepository.getProducts({
        params: {
          companyId: user?.companyId!,
          integrationId: selectedIntegrationId!!,
        },
        query: {
          page: page + 1,
          limit: rowsPerPage,
          nome: debouncedFilters.query,
        },
      }),
    onError: (error) => {
      const isAppError = error instanceof AppError;
      const title = isAppError
        ? error.message
        : 'Não foi possível carregar os produtos.\nTente novamente mais tarde';

      enqueueSnackbar(title, { variant: 'error' });

      push(paths.dashboard.root);
    },
    retry: 1,
    refetchOnWindowFocus: false,
    keepPreviousData: true,
    cacheTime: 1000 * 60 * 10, // 10 minutes
    staleTime: 1000 * 60 * 10, // 10 minutes,
    enabled: !!selectedIntegrationId,
  });

  const tableData = products?.data || [];
  const isNotFound = tableData.length <= 0 && !isLoading;

  const onSetQuery = useCallback((query: string) => {
    setFilters((prev) => ({
      ...prev,
      query,
    }));
  }, []);

  const onSetField = useCallback((megaField: string) => {
    const formattedFieldParams = ['email', 'name', 'cpfCnpj', 'phoneNumber']?.includes(megaField)
      ? megaField
      : 'name';

    setFilters((prev) => ({
      ...prev,
      field: formattedFieldParams as any,
    }));
  }, []);

  useEffect(() => {
    push(`?page=${page}&pageSize=${rowsPerPage}`);
  }, []);

  useEffect(() => {
    setPage(0);

    const searchParams = new URLSearchParams();
    searchParams.set('field', debouncedFilters.field);
    searchParams.set('query', debouncedFilters.query);

    push(`?${searchParams.toString()}`);
  }, [debouncedFilters]);

  return (
    <>
      <Container maxWidth={false}>
        <CustomBreadcrumbs
          heading="Lista de Produtos Bling"
          links={[{ name: 'Início', href: paths.dashboard.root }, { name: 'Produtos Bling' }]}
          sx={{ mb: { xs: 3, md: 5 } }}
          action={
            <>
              {blingIntegrations.length > 1 && (
                <Select
                  sx={{
                    height: 40,
                    cursor: 'pointer',
                    '&:hover': {
                      border: (theme) => `solid 1px ${theme.palette.primary[theme.palette.mode]}`,
                    },
                    py: 1,
                    border: (theme) => `1px solid ${alpha(theme.palette.background.default, 0.3)}`,
                    bgcolor: (theme) => theme.palette.background.neutral,
                    borderRadius: 1,
                  }}
                  value={selectedIntegrationId || ''}
                  onChange={(event) => {
                    const integrationId = event.target.value;

                    if (integrationId !== '') {
                      setIntegrationId(Number(integrationId));
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    Selecione uma Integração
                  </MenuItem>

                  {blingIntegrations?.map((integration) => (
                    <MenuItem key={integration.id} value={integration.id}>
                      {integration.name}
                    </MenuItem>
                  ))}
                </Select>
              )}
            </>
          }
        />
        <Card>
          <BlingProductTableToolbar
            filters={filters}
            onFilters={handleFilters}
            onResetFilters={handleResetFilters}
            canReset={canReset}
            onSetField={onSetField}
            onSetQuery={onSetQuery}
          />

          <TableContainer sx={{ minWidth: '100%', maxHeight: '65vh' }}>
            <Table stickyHeader size={dense ? 'small' : 'medium'}>
              <TableHeadCustom order={order} orderBy={orderBy} headLabel={TABLE_HEAD} />

              <TableBody>
                {isLoading || isFetching ? (
                  <TableSkeletonCustom rows={pageSize} />
                ) : (
                  <>
                    {tableData.map((row) => (
                      <BlingProductTableRow row={row} key={row.id} />
                    ))}
                    <TableNoData notFound={isNotFound} />
                  </>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePaginationCustom
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={onChangePage}
            onRowsPerPageChange={onChangeRowsPerPage}
            count={products?.meta?.totalCount ?? 0}
            // dense
            dense={dense}
            onChangeDense={onChangeDense}
          />
        </Card>
      </Container>
    </>
  );
}

function TableSkeletonCustom({ rows = 5 }: { rows?: number }) {
  return (
    <>
      {Array.from({ length: rows }).map((_, index) => (
        <TableSkeleton key={index} />
      ))}
    </>
  );
}
