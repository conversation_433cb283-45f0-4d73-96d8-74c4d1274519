import { <PERSON>ge, Button, MenuItem, Select, Stack } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { useQueries } from '@tanstack/react-query';
import { isValid } from 'date-fns';
import { useEffect, useMemo, useState } from 'react';
import Iconify from 'src/components/iconify';
import { TableChipFilter } from 'src/components/table/table-filter-chip';
import { TableFilterChipInput } from 'src/components/table/table-filter-chip-input';
import { TableFilterChipMultiSelect } from 'src/components/table/table-filter-chip-multi-select';
import { TableQueryInput } from 'src/components/table/table-query-input';
import { useAuthContext } from 'src/contexts/auth/hooks';
import { useBlingContext } from 'src/contexts/integrations/bling/bling-context';
import { useBoolean } from 'src/hooks/use-boolean';
import { BlingOrderRepository } from 'src/repositories/integrations/bling/bling-order-repository';
import { IBlingOrderFilters } from 'src/types/integrations/bling/order';
import { IUser } from 'src/types/user';
import { fDateTime } from 'src/utils/format-time';

export type DateFilterType = 'between' | 'greater' | 'less' | 'equal';

type Props = {
  filters: IBlingOrderFilters;
  setFilters: (name: string, value: any) => void;
  canReset: boolean;
  onResetFilters: () => void;
};

const INPUT_HEIGHT = 50;

export function BlingOrderTableToolbar({ filters, setFilters, onResetFilters, canReset }: Props) {
  const { user } = useAuthContext();

  const { selectedIntegrationId } = useBlingContext();

  const companyId = user?.companyId!;

  const showFilters = useBoolean();

  const [orderDateFilter, setOrderDateFilter] = useState<{
    type: DateFilterType;
    firstDate: Date | null;
    secondDate?: Date | null;
  }>({
    type: 'between',
    firstDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    secondDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  });

  const [deliveryForecastDateFilter, setDeliveryForecastDateFilter] = useState<{
    type: DateFilterType;
    firstDate: Date | null;
    secondDate?: Date | null;
  }>({
    type: 'between',
    firstDate: null,
    secondDate: null,
  });

  const [{ data = [] }, { data: carriers = [] }] = useQueries({
    queries: [
      {
        refetchOnWindowFocus: false,
        queryFn: () => BlingOrderRepository.statuses(companyId, selectedIntegrationId!!),
        queryKey: ['bling-order-statuses'],
        cacheTime: 1000 * 60 * 60 * 78, // 78 hours
        staleTime: 1000 * 60 * 60 * 78, // 78 hours
      },
      {
        refetchOnWindowFocus: false,
        queryFn: () => BlingOrderRepository.carriers(companyId, selectedIntegrationId!!),
        queryKey: ['bling-order-carriers'],
        cacheTime: 1000 * 60 * 60 * 78, // 78 hours
        staleTime: 1000 * 60 * 60 * 78, // 78 hours
      },
    ],
  });

  const orderStatuses = useMemo(() => {
    return data.map((status) => ({
      value: status.rId,
      label: status.rNome ?? '',
      color: status.rCor,
    }));
  }, [data]);

  const carriersToList = useMemo(() => {
    return carriers.map((carrier) => ({
      value: carrier,
      label: carrier,
    }));
  }, [carriers]);

  const [filterHeight, setFilterHeight] = useState(1);

  useEffect(() => {
    const element = document.getElementById('filter-chips');
    if (element) {
      const resizeObserver = new ResizeObserver(() => {
        setFilterHeight(element.clientHeight || 1);
      });

      resizeObserver.observe(element);

      return () => resizeObserver.disconnect();
    }
  }, []);

  const validateDate = (date: Date | null | undefined) => !isNaN(date?.getTime() || NaN);

  return (
    <Stack
      alignItems={{ xs: 'flex-end' }}
      direction={{
        xs: 'column',
      }}
      sx={{
        p: 2,
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        spacing={2}
        flex={1}
        maxHeight={INPUT_HEIGHT}
        minHeight={INPUT_HEIGHT}
        sx={{
          width: '100%',
        }}
      >
        <TableQueryInput
          value={filters.orderCode}
          onChangeValue={(v) => setFilters('orderCode', v)}
          placeholder="Pesquisar por código de pedido..."
        />

        <Badge color="primary" overlap="circular" variant="dot" invisible={!canReset}>
          <Button
            onClick={showFilters.onToggle}
            variant="outlined"
            endIcon={<Iconify icon="mynaui:filter-solid" width={28} height={28} />}
          >
            Filtros
          </Button>
        </Badge>
      </Stack>

      <Stack
        sx={{
          width: '100%',
          height: showFilters.value ? filterHeight : 0,
          position: 'relative',
          overflow: 'hidden',
          transition: (theme) =>
            theme.transitions.create(['height'], {
              duration: theme.transitions.duration.shortest,
            }),
        }}
      />

      <Stack
        direction="row"
        flexWrap="wrap"
        alignItems="center"
        spacing={1}
        flex={1}
        id="filter-chips"
        sx={{
          opacity: showFilters.value ? 1 : 0,
          position: 'absolute',
          left: 0,
          px: 2,
          marginTop: `${INPUT_HEIGHT + 2 * 4}px`,
          transition: (theme) =>
            theme.transitions.create(['opacity'], {
              duration: theme.transitions.duration.shortest,
            }),
        }}
      >
        <TableFilterChipMultiSelect
          label="Status"
          onClear={() => setFilters('orderStatuses', [])}
          data={filters.orderStatuses}
          onChange={(value) => setFilters('orderStatuses', value)}
          options={orderStatuses}
        />

        <TableFilterChipMultiSelect
          label="Transportadoras"
          onClear={() => setFilters('orderCarriers', [])}
          data={filters.orderCarriers}
          onChange={(value) => setFilters('orderCarriers', value)}
          options={carriersToList}
        />

        {[
          {
            label: 'Data do pedido',
            filter: orderDateFilter,
            setFilter: setOrderDateFilter,
            key: 'orderDate',
            rootFilters: filters.orderDate,
          },
          {
            label: 'Previsão entrega',
            filter: deliveryForecastDateFilter,
            setFilter: setDeliveryForecastDateFilter,
            key: 'deliveryForecastDate',
            rootFilters: filters.deliveryForecastDate,
          },
        ].map(({ label, filter, setFilter, rootFilters, key }) => (
          <TableChipFilter
            key={key}
            label={label}
            result={
              rootFilters?.firstDate
                ? rootFilters.type === 'between'
                  ? `${fDateTime(rootFilters.firstDate, 'dd/MM/yyyy')} - ${fDateTime(
                      rootFilters.secondDate!,
                      'dd/MM/yyyy',
                    )}`
                  : `${fDateTime(rootFilters.firstDate, 'dd/MM/yyyy')}`
                : undefined
            }
            onConfirm={() => {
              if (
                validateDate(filter.firstDate) &&
                (filter.type !== 'between' || validateDate(filter.secondDate))
              ) {
                if (filter.type === 'between') {
                  if (
                    filter.firstDate &&
                    filter.secondDate &&
                    isValid(filter.firstDate) &&
                    isValid(filter.secondDate) &&
                    filter.firstDate < filter.secondDate
                  ) {
                    setFilters(key, filter);
                  }
                } else {
                  if (filter.firstDate && isValid(filter.firstDate)) {
                    setFilters(key, filter);
                  }
                }
              }
            }}
            onClear={() => {
              setFilters(key, undefined);
              setFilter({ type: 'between', firstDate: null, secondDate: null });
            }}
            popoverContent={
              <Stack spacing={2} sx={{ p: 2 }}>
                <Select
                  value={filter.type}
                  onChange={(e) =>
                    setFilter((prev) => ({
                      ...prev,
                      type: e.target.value as DateFilterType,
                    }))
                  }
                  fullWidth
                >
                  <MenuItem value="between">Entre</MenuItem>
                  <MenuItem value="greater">Maior que</MenuItem>
                  <MenuItem value="less">Menor que</MenuItem>
                  <MenuItem value="equal">Igual</MenuItem>
                </Select>
                <DatePicker
                  label="Data Inicial"
                  value={filter.firstDate}
                  onChange={(newValue) =>
                    setFilter((prev) => ({
                      ...prev,
                      firstDate: isValid(newValue) ? newValue : prev.firstDate,
                    }))
                  }
                  slotProps={{ textField: { fullWidth: true } }}
                />

                {filter.type === 'between' && (
                  <DatePicker
                    label="Data Final"
                    value={filter.secondDate}
                    onChange={(newValue) =>
                      setFilter((prev) => ({
                        ...prev,
                        secondDate: isValid(newValue) ? newValue : prev.secondDate,
                      }))
                    }
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                )}
              </Stack>
            }
          />
        ))}

        <TableChipFilter
          label="Ordenar por"
          result={`${
            filters.orderBy === 'orderDate' ? 'Data do Pedido' : 'Previsão de Entrega'
          } - ${filters.orderDirection === 'ASC' ? 'Crescente' : 'Decrescente'}`}
          popoverContent={
            <Stack spacing={2} sx={{ p: 2 }}>
              <Select
                value={filters.orderBy}
                onChange={(e) => setFilters('orderBy', e.target.value)}
                fullWidth
              >
                <MenuItem value="orderDate">Data do Pedido</MenuItem>
                <MenuItem value="deliveryForecastDate">Previsão de Entrega</MenuItem>
              </Select>

              <Select
                value={filters.orderDirection}
                onChange={(e) => setFilters('orderDirection', e.target.value)}
                fullWidth
              >
                <MenuItem value="ASC">Crescente</MenuItem>
                <MenuItem value="DESC">Decrescente</MenuItem>
              </Select>
            </Stack>
          }
          onClear={() => {
            setFilters('orderBy', 'orderDate');
            setFilters('orderDirection', 'DESC');
          }}
        />

        <TableFilterChipInput
          label="Nome"
          onChange={(v) => setFilters('clientName', v)}
          value={filters.clientName}
        />

        {canReset && (
          <Button color="primary" variant="text" onClick={onResetFilters}>
            Limpar filtros
          </Button>
        )}
      </Stack>
    </Stack>
  );
}
