import { useFieldArray, useFormContext } from 'react-hook-form';
import { useEffect, useRef, useCallback } from 'react';

export interface Parcela {
  id: string;
  dias: number;
  data: string | Date;
  valor: number;
  formaPagamento: {
    data: {
      id: number;
      descricao: string;
    };
  } | null;
  observacao?: string;
}

export interface UsePaymentInstallmentsReturn {
  parcelas: Parcela[];
  addParcela: () => void;
  removeParcela: (index: number) => void;

  updateParcela: (index: number, field: keyof Parcela, value: any, baseDate: Date) => void;
  generateParcelas: (condicao: string, totalValue: number, baseDate: Date) => void;
  recalculateParcelas: (newTotalValue: number) => void;
  totalParcelas: number;
}

export function usePaymentInstallments(): UsePaymentInstallmentsReturn {
  const { control, watch } = useFormContext();
  const lastTotalValueRef = useRef<number>(0);
  const currentTotalValueRef = useRef<number>(0);
  const isUpdatingRef = useRef<boolean>(false);

  const {
    fields: parcelas,
    append,
    remove,
    update,
  } = useFieldArray({
    control,
    name: 'orderDetails.condicoesPagamento.parcelas',
  });

  const recalculateParcelas = useCallback(
    (newTotalValue: number) => {
      if (isUpdatingRef.current) return;

      currentTotalValueRef.current = newTotalValue;

      if (parcelas.length === 0) {
        lastTotalValueRef.current = newTotalValue;
        return;
      }

      // Evita recálculo se o valor não mudou significativamente
      if (Math.abs(lastTotalValueRef.current - newTotalValue) < 0.01) return;

      isUpdatingRef.current = true;
      const valorPorParcela = newTotalValue / parcelas.length;

      parcelas.forEach((_, index) => {
        const currentParcela = parcelas[index] as any;
        update(index, {
          ...currentParcela,
          valor: valorPorParcela,
        });
      });

      lastTotalValueRef.current = newTotalValue;

      // Reset flag after update
      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 50);
    },
    [parcelas, update],
  );

  const addParcela = () => {
    const newParcela: Parcela = {
      id: `parcela-${parcelas.length + 1}`,
      dias: 0,
      data: new Date(),
      valor:
        currentTotalValueRef.current > 0 ? currentTotalValueRef.current / (parcelas.length + 1) : 0,
      formaPagamento: null,
      observacao: '',
    };
    append(newParcela);
  };

  const removeParcela = (index: number) => {
    remove(index);
  };

  useEffect(() => {
    if (!isUpdatingRef.current && parcelas.length > 0 && currentTotalValueRef.current > 0) {
      const valorPorParcela = currentTotalValueRef.current / parcelas.length;
      const watchedParcelas =
        (watch('orderDetails.condicoesPagamento.parcelas') as Parcela[]) || [];

      const needsUpdate = watchedParcelas.some(
        (parcela: any) => Math.abs((parcela?.valor ?? 0) - valorPorParcela) > 0.01,
      );

      if (needsUpdate) {
        isUpdatingRef.current = true;
        parcelas.forEach((_, parcelaIndex) => {
          const currentParcela = watchedParcelas[parcelaIndex] as any;
          update(parcelaIndex, {
            ...currentParcela,
            valor: valorPorParcela,
          });
        });

        setTimeout(() => {
          isUpdatingRef.current = false;
        }, 50);
      }
    }
  }, [parcelas.length, parcelas, update, watch]);

  const updateParcela = (index: number, field: keyof Parcela, value: any, baseDate: Date) => {
    const currentParcela = parcelas[index] as any;
    const updatedParcela = {
      ...currentParcela,
      [field]: value,
    };

    // Se o campo alterado for 'dias', recalcular a data
    if (field === 'dias' && typeof value === 'number') {
      const dataVencimento = new Date(baseDate);
      dataVencimento.setDate(dataVencimento.getDate() + value);
      updatedParcela.data = dataVencimento;
    }

    update(index, updatedParcela);
  };

  const generateParcelas = (condicao: string, totalValue: number, baseDate: Date) => {
    // Parse da condição de pagamento (ex: "160 30 60")
    const diasArray = condicao
      .split(' ')
      .map((d) => parseInt(d.trim()))
      .filter((d) => !isNaN(d));

    if (diasArray.length === 0) return;

    const valorPorParcela = totalValue / diasArray.length;

    // Remover parcelas existentes (de trás para frente)
    for (let i = parcelas.length - 1; i >= 0; i--) {
      remove(i);
    }

    // Adicionar novas parcelas
    diasArray.forEach((dias, index) => {
      const dataVencimento = new Date(baseDate);
      dataVencimento.setDate(dataVencimento.getDate() + dias);

      append({
        id: `parcela-${index + 1}`,
        dias,
        data: dataVencimento,
        valor: valorPorParcela,
        formaPagamento: null,
        observacao: '',
      });
    });

    lastTotalValueRef.current = totalValue;
    currentTotalValueRef.current = totalValue;
  };

  const watchedParcelas = (watch('orderDetails.condicoesPagamento.parcelas') as Parcela[]) || [];
  const totalParcelas = (watchedParcelas as Parcela[]).reduce(
    (sum, p) => sum + (Number(p?.valor) || 0),
    0,
  );

  return {
    parcelas: parcelas as Parcela[],
    addParcela,
    removeParcela,
    updateParcela,
    generateParcelas,
    recalculateParcelas,
    totalParcelas,
  };
}
