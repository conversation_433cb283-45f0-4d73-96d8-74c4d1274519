import { useState, useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuthContext } from 'src/contexts/auth/hooks';
import { useDebounce } from 'src/hooks/use-debounce';
import { IBlingProduct } from 'src/types/integrations/bling/product';
import { BlingProductRepository } from 'src/repositories/integrations/bling/bling-product-repository';

interface UseBlingProductSearchReturn {
  searchValue: string;
  setSearchValue: (value: string) => void;
  selectedProduct: IBlingProduct | null;
  setSelectedProduct: (product: IBlingProduct | null) => void;
  products: IBlingProduct[];
  isLoading: boolean;
}

export function useBlingProductSearch(
  integrationId: number,
  initialProductNome?: string,
): UseBlingProductSearchReturn {
  const { company } = useAuthContext();
  const [searchValue, setSearchValue] = useState('');
  const [selectedProduct, setSelectedProduct] = useState<IBlingProduct | null>(null);

  const filters = useMemo(
    () => ({
      page: 1,
      limit: searchValue ? 99 : 10,
      nome: searchValue,
    }),
    [searchValue],
  );

  const debouncedFilters = useDebounce(filters, 300);

  const { data: initialProductData } = useQuery({
    queryKey: ['bling-product-initial', company?.id, integrationId, initialProductNome],
    queryFn: () =>
      BlingProductRepository.getProducts({
        params: {
          companyId: company!.id,
          integrationId,
        },
        query: {
          page: 1,
          limit: 1,
          nome: initialProductNome,
        },
      }),
    enabled: !!company?.id && !!integrationId && !!initialProductNome && !selectedProduct,
  });

  useEffect(() => {
    if (initialProductData?.data.length && !selectedProduct) {
      const product = initialProductData?.data[0];
      setSelectedProduct(product);
      setSearchValue(product.data.nome || '');
    }
  }, [initialProductData, selectedProduct]);

  // Buscar lista de produtos
  const { data: productsList = null, isLoading } = useQuery({
    queryKey: ['bling-products-search', company?.id, integrationId, debouncedFilters],
    queryFn: () =>
      BlingProductRepository.getProducts({
        params: {
          companyId: company!.id,
          integrationId,
        },
        query: debouncedFilters,
      }),
    enabled: !!company?.id && !!integrationId,
    keepPreviousData: true,
  });

  return {
    searchValue,
    setSearchValue,
    selectedProduct,
    setSelectedProduct,
    products: productsList?.data || [],
    isLoading,
  };
}
