'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Stack,
  Autocomplete,
  TextField,
  CircularProgress,
  Box,
  useTheme,
  Button,
  Collapse,
  Grid,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useSnackbar } from 'notistack';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as Yup from 'yup';
import { IBlingClientDetails, ICreateBlingClient } from 'src/types/integrations/bling/client';
import { useFormContext } from 'react-hook-form';
import { useBlingClientSearch } from './hooks/use-bling-client-search';
import { BlingClientRepository } from 'src/repositories/integrations/bling/bling-client-repository';
import { useAuthContext } from 'src/contexts/auth/hooks';
import Iconify from 'src/components/iconify';

// Validation schema for new client form
const newClientSchema = Yup.object({
  nome: Yup.string().required('Nome é obrigatório').min(2, 'Nome deve ter pelo menos 2 caracteres'),
  numeroDocumento: Yup.string().required('Documento é obrigatório'),
  tipo: Yup.string()
    .required('Tipo é obrigatório')
    .oneOf(['F', 'J'], 'Tipo deve ser F (Física) ou J (Jurídica)'),
  celular: Yup.string().required('Celular é obrigatório'),
  endereco: Yup.object({
    geral: Yup.object({
      endereco: Yup.string().required('Endereço é obrigatório'),
      cep: Yup.string().required('CEP é obrigatório'),
      bairro: Yup.string().required('Bairro é obrigatório'),
      municipio: Yup.string().required('Município é obrigatório'),
      uf: Yup.string().required('UF é obrigatório').length(2, 'UF deve ter 2 caracteres'),
      numero: Yup.string().required('Número é obrigatório'),
      complemento: Yup.string().optional(),
    }),
  }),
});

type NewClientFormData = Yup.InferType<typeof newClientSchema>;

interface BlingOrderClientStepProps {
  integrationId: number;
}

export default function BlingOrderClientStep({ integrationId }: BlingOrderClientStepProps) {
  const theme = useTheme();
  const { setValue, watch } = useFormContext();
  const { enqueueSnackbar } = useSnackbar();
  const { company } = useAuthContext();
  const queryClient = useQueryClient();

  const selectedClientId = watch('contato.id');

  // State for collapsible form
  const [isFormOpen, setIsFormOpen] = useState(false);

  const {
    searchValue,
    setSearchValue,
    selectedClient,
    setSelectedClient,
    clients,
    isLoading,
    isLoadingDetails,
  } = useBlingClientSearch(integrationId, selectedClientId);

  // Form for new client creation
  const newClientForm = useForm<NewClientFormData>({
    resolver: yupResolver(newClientSchema),
    defaultValues: {
      nome: '',
      numeroDocumento: '',
      tipo: 'F',
      celular: '',
      endereco: {
        geral: {
          endereco: '',
          cep: '',
          bairro: '',
          municipio: '',
          uf: '',
          numero: '',
          complemento: '',
        },
      },
    },
  });

  // Mutation for creating new client
  const createClientMutation = useMutation({
    mutationFn: (clientData: ICreateBlingClient) =>
      BlingClientRepository.create(company!.id, integrationId, clientData),
    onSuccess: (newClient) => {
      enqueueSnackbar('Cliente criado com sucesso!', { variant: 'success' });

      // Update the client selection
      handleClientSelect(newClient);

      // Reset form and close it
      newClientForm.reset();
      setIsFormOpen(false);

      // Invalidate queries to refresh the client list
      queryClient.invalidateQueries(['bling-clients-search']);
      queryClient.invalidateQueries(['bling-clients-details']);
    },
    onError: (error: any) => {
      enqueueSnackbar(error?.response?.data?.message || 'Erro ao criar cliente', {
        variant: 'error',
      });
    },
  });

  const handleClientSelect = (client: IBlingClientDetails) => {
    setValue('contato.id', client.data.id);
    setValue('contato.numeroDocumento', client.data.numeroDocumento);
    setValue('contato.tipoPessoa', client.data.tipo);

    setSelectedClient(client);
  };

  // Handler for opening/closing the new client form
  const handleCreateNewClient = () => {
    setIsFormOpen(!isFormOpen);
  };

  // Handler for submitting the new client form
  const handleSubmitNewClient = (data: NewClientFormData) => {
    const clientData: ICreateBlingClient = {
      ...data,
      situacao: 'A', // Active status
      endereco: {
        geral: {
          ...data.endereco.geral,
          complemento: data.endereco.geral.complemento || '', // Ensure complemento is always a string
        },
      },
    };

    createClientMutation.mutate(clientData);
  };

  return (
    <Stack spacing={2}>
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Dados do Cliente
          </Typography>

          <Stack spacing={2}>
            <Autocomplete
              clearOnBlur
              blurOnSelect
              loading={isLoading}
              options={clients || []}
              filterOptions={(opts, state) => {
                const filtered = opts.filter(() => {
                  if (!state.inputValue) return true;
                  return true;
                });
                return filtered;
              }}
              getOptionLabel={(option) => option.data?.nome || ''}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              renderOption={(props, option) => (
                <Box
                  component="li"
                  {...props}
                  sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}
                >
                  <Typography variant="body1" sx={{ fontWeight: theme.typography.fontWeightBold }}>
                    {option.data?.nome}
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8em', color: 'gray' }}>
                    {option.data?.numeroDocumento || 'Sem documento'} - {option.data?.email}
                  </Typography>
                </Box>
              )}
              onChange={(_, value) => {
                if (value) {
                  handleClientSelect(value);
                }
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  label="Procure pelos clientes"
                  placeholder="Pesquise um cliente ou veja os disponíveis"
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {(isLoading || isLoadingDetails) && (
                          <CircularProgress color="inherit" size={20} />
                        )}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
              value={selectedClient}
              inputValue={searchValue}
              onInputChange={(_, value) => {
                setSearchValue(value);
              }}
            />

            <Button
              variant="contained"
              onClick={handleCreateNewClient}
              startIcon={<Iconify icon={isFormOpen ? 'eva:minus-fill' : 'eva:plus-fill'} />}
            >
              {isFormOpen ? 'Cancelar' : 'Criar Novo Cliente'}
            </Button>

            {/* Collapsible form for creating new client */}
            <Collapse in={isFormOpen} timeout="auto" unmountOnExit>
              <Card sx={{ mt: 2, bgcolor: 'background.neutral' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Criar Novo Cliente
                  </Typography>

                  <Box
                    component="form"
                    onSubmit={newClientForm.handleSubmit(handleSubmitNewClient)}
                  >
                    <Stack spacing={3}>
                      {/* Basic Information */}
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Nome *"
                            {...newClientForm.register('nome')}
                            error={!!newClientForm.formState.errors.nome}
                            helperText={newClientForm.formState.errors.nome?.message}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Documento (CPF/CNPJ) *"
                            {...newClientForm.register('numeroDocumento')}
                            error={!!newClientForm.formState.errors.numeroDocumento}
                            helperText={newClientForm.formState.errors.numeroDocumento?.message}
                          />
                        </Grid>
                      </Grid>

                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            select
                            label="Tipo *"
                            {...newClientForm.register('tipo')}
                            error={!!newClientForm.formState.errors.tipo}
                            helperText={newClientForm.formState.errors.tipo?.message}
                            SelectProps={{ native: true }}
                          >
                            <option value="F">Pessoa Física</option>
                            <option value="J">Pessoa Jurídica</option>
                          </TextField>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Celular *"
                            {...newClientForm.register('celular')}
                            error={!!newClientForm.formState.errors.celular}
                            helperText={newClientForm.formState.errors.celular?.message}
                          />
                        </Grid>
                      </Grid>

                      {/* Address Information */}
                      <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>
                        Endereço
                      </Typography>

                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={8}>
                          <TextField
                            fullWidth
                            label="Endereço *"
                            {...newClientForm.register('endereco.geral.endereco')}
                            error={!!newClientForm.formState.errors.endereco?.geral?.endereco}
                            helperText={
                              newClientForm.formState.errors.endereco?.geral?.endereco?.message
                            }
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            fullWidth
                            label="Número *"
                            {...newClientForm.register('endereco.geral.numero')}
                            error={!!newClientForm.formState.errors.endereco?.geral?.numero}
                            helperText={
                              newClientForm.formState.errors.endereco?.geral?.numero?.message
                            }
                          />
                        </Grid>
                      </Grid>

                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            fullWidth
                            label="CEP *"
                            {...newClientForm.register('endereco.geral.cep')}
                            error={!!newClientForm.formState.errors.endereco?.geral?.cep}
                            helperText={
                              newClientForm.formState.errors.endereco?.geral?.cep?.message
                            }
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            fullWidth
                            label="Bairro *"
                            {...newClientForm.register('endereco.geral.bairro')}
                            error={!!newClientForm.formState.errors.endereco?.geral?.bairro}
                            helperText={
                              newClientForm.formState.errors.endereco?.geral?.bairro?.message
                            }
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            fullWidth
                            label="Complemento"
                            {...newClientForm.register('endereco.geral.complemento')}
                            error={!!newClientForm.formState.errors.endereco?.geral?.complemento}
                            helperText={
                              newClientForm.formState.errors.endereco?.geral?.complemento?.message
                            }
                          />
                        </Grid>
                      </Grid>

                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={8}>
                          <TextField
                            fullWidth
                            label="Município *"
                            {...newClientForm.register('endereco.geral.municipio')}
                            error={!!newClientForm.formState.errors.endereco?.geral?.municipio}
                            helperText={
                              newClientForm.formState.errors.endereco?.geral?.municipio?.message
                            }
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            fullWidth
                            label="UF *"
                            {...newClientForm.register('endereco.geral.uf')}
                            error={!!newClientForm.formState.errors.endereco?.geral?.uf}
                            helperText={newClientForm.formState.errors.endereco?.geral?.uf?.message}
                            inputProps={{ maxLength: 2, style: { textTransform: 'uppercase' } }}
                          />
                        </Grid>
                      </Grid>

                      {/* Form Actions */}
                      <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
                        <LoadingButton
                          type="submit"
                          variant="contained"
                          loading={createClientMutation.isLoading}
                          startIcon={<Iconify icon="eva:save-fill" />}
                        >
                          Criar Cliente
                        </LoadingButton>
                        <Button
                          variant="outlined"
                          onClick={handleCreateNewClient}
                          disabled={createClientMutation.isLoading}
                        >
                          Cancelar
                        </Button>
                      </Stack>
                    </Stack>
                  </Box>
                </CardContent>
              </Card>
            </Collapse>
          </Stack>
        </CardContent>
      </Card>
    </Stack>
  );
}
