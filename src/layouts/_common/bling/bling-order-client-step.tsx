'use client';

import {
  Typo<PERSON>,
  <PERSON>,
  CardContent,
  Stack,
  Autocomplete,
  TextField,
  CircularProgress,
  Box,
  useTheme,
  Button,
} from '@mui/material';
import { IBlingClientDetails } from 'src/types/integrations/bling/client';
import { useFormContext } from 'react-hook-form';
import { useBlingClientSearch } from './hooks/use-bling-client-search';
import { useRouter } from 'next/navigation';

interface BlingOrderClientStepProps {
  integrationId: number;
}

export default function BlingOrderClientStep({ integrationId }: BlingOrderClientStepProps) {
  const theme = useTheme();
  const { setValue, watch } = useFormContext();
  const router = useRouter();

  const selectedClientId = watch('contato.id');

  const {
    searchValue,
    setSearchValue,
    selectedClient,
    setSelectedClient,
    clients,
    isLoading,
    isLoadingDetails,
  } = useBlingClientSearch(integrationId, selectedClientId);

  const handleClientSelect = (client: IBlingClientDetails) => {
    setValue('contato.id', client.data.id);
    setValue('contato.numeroDocumento', client.data.numeroDocumento);
    setValue('contato.tipoPessoa', client.data.tipo);

    setSelectedClient(client);
  };

  return (
    <Stack spacing={2}>
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Dados do Cliente
          </Typography>

          <Stack spacing={2}>
            <Autocomplete
              clearOnBlur
              blurOnSelect
              loading={isLoading}
              options={clients || []}
              filterOptions={(opts, state) => {
                const filtered = opts.filter(() => {
                  if (!state.inputValue) return true;
                  return true;
                });
                return filtered;
              }}
              getOptionLabel={(option) => option.data?.nome || ''}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              renderOption={(props, option) => (
                <Box
                  component="li"
                  {...props}
                  sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}
                >
                  <Typography variant="body1" sx={{ fontWeight: theme.typography.fontWeightBold }}>
                    {option.data?.nome}
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8em', color: 'gray' }}>
                    {option.data?.numeroDocumento || 'Sem documento'} - {option.data?.email}
                  </Typography>
                </Box>
              )}
              onChange={(_, value) => {
                if (value) {
                  handleClientSelect(value);
                }
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  label="Procure pelos clientes"
                  placeholder="Pesquise um cliente ou veja os disponíveis"
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {(isLoading || isLoadingDetails) && (
                          <CircularProgress color="inherit" size={20} />
                        )}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
              value={selectedClient}
              inputValue={searchValue}
              onInputChange={(_, value) => {
                setSearchValue(value);
              }}
            />

            <Button variant="contained" onClick={() => handleCreateNewClient()}>
              Novo Cliente
            </Button>
          </Stack>
        </CardContent>
      </Card>
    </Stack>
  );
}
