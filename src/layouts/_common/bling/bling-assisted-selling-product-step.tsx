'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Autocomplete,
  TextField,
  CircularProgress,
  Box,
  Paper,
  Grid,
  IconButton,
  useTheme,
} from '@mui/material';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { IBlingProduct } from 'src/types/integrations/bling/product';
import type { OrderItem } from 'src/utils/bling/order-payload-builder';
import { fCurrency } from 'src/utils/format-number';
import Iconify from 'src/components/iconify';
import { RHFNumberInput, RHFTextField } from 'src/components/hook-form';
import TextMaxLine from 'src/components/text-max-line';
import RHFNumericField from 'src/components/hook-form/rhf-numericField';
import { useBlingProductSearch } from './hooks/use-bling-product-search';

interface BlingAssistedSellingProductStepProps {
  integrationId: number;
}

export default function BlingAssistedSellingProductStep({
  integrationId,
}: BlingAssistedSellingProductStepProps) {
  const theme = useTheme();
  const { control, watch, setValue } = useFormContext();

  const { fields, append, remove, update } = useFieldArray({
    control,
    name: '_selectedProducts',
  });

  // Watch para monitorar mudanças em tempo real
  const watchedProducts = watch('_selectedProducts') || [];

  const { searchValue, setSearchValue, selectedProduct, setSelectedProduct, products, isLoading } =
    useBlingProductSearch(integrationId);

  const handleProductSelect = (product: IBlingProduct) => {
    const existingIndex = fields.findIndex((field) => (field as any).product.id === product.id);

    if (existingIndex >= 0) {
      // Incrementar quantidade se já existe - usa setValue para garantir sincronização
      const currentProduct = watchedProducts[existingIndex] || fields[existingIndex];
      const currentQuantity = currentProduct?.quantity || 1;
      const newQuantity = currentQuantity + 1;

      setValue(`_selectedProducts.${existingIndex}.quantity`, newQuantity, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    } else {
      // Adicionar novo produto
      append({
        product,
        quantity: 1,
        unitPrice: product.data?.preco || 0,
        discount: 0,
      });
    }
    setSearchValue('');
  };

  return (
    <Stack spacing={3}>
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Selecionar Produtos
          </Typography>

          <Autocomplete
            clearOnBlur
            blurOnSelect
            loading={isLoading && searchValue.length >= 3}
            options={products || []}
            filterOptions={(opts) => opts}
            getOptionLabel={(option) => option?.data?.nome || ''}
            isOptionEqualToValue={(option, value) => option?.id === value?.id}
            renderOption={(props, option) => (
              <Box
                component="li"
                {...props}
                sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}
              >
                <Typography variant="body1" sx={{ fontWeight: theme.typography.fontWeightBold }}>
                  <TextMaxLine line={1}>{option?.data?.nome}</TextMaxLine>
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8em', color: 'gray' }}>
                  Código: {option?.data?.codigo} • Preço: {fCurrency(option?.data?.preco)}
                </Typography>
              </Box>
            )}
            onChange={(_, value) => {
              if (value?.id) {
                handleProductSelect(value);
              }
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                label="Procure pelos produtos"
                placeholder="Pesquise um produto com ao menos 3 caracteres"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isLoading && searchValue.length >= 3 && (
                        <CircularProgress color="inherit" size={20} />
                      )}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            value={null}
            inputValue={searchValue}
            onInputChange={(_, value) => {
              setSearchValue(value);
            }}
          />
        </CardContent>
      </Card>

      {fields.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Produtos Selecionados ({fields.length})
            </Typography>

            <Stack spacing={2}>
              {fields.map((item: any, index: number) => {
                const currentProduct = watchedProducts[index] || item;
                const unitPrice = currentProduct.unitPrice || 0;
                const quantity = currentProduct.quantity || 1;
                const discount = currentProduct.discount || 0;
                const totalPrice = unitPrice * quantity * (1 - discount / 100);

                return (
                  <Paper key={item.product.id} variant="outlined" sx={{ p: 2 }}>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} md={4}>
                        <TextMaxLine variant="body1" line={1} sx={{ wordBreak: 'break-all' }}>
                          {item.product.data?.nome}
                        </TextMaxLine>
                        <Typography variant="body2" color="text.secondary">
                          Código: {item.product.data?.codigo}
                        </Typography>
                      </Grid>

                      <Grid item xs={6} md={2}>
                        <RHFTextField
                          name={`_selectedProducts.${index}.quantity`}
                          label="Quantidade"
                          type="number"
                          inputProps={{ min: 1 }}
                          sx={{ width: '100%' }}
                        />
                      </Grid>

                      <Grid item xs={6} md={2}>
                        <RHFNumericField
                          name={`_selectedProducts.${index}.unitPrice`}
                          label="Preço Unit."
                          inputProps={{ min: 0, step: 0.01 }}
                          sx={{ width: '100%' }}
                        />
                      </Grid>

                      <Grid item xs={6} md={2}>
                        <RHFNumberInput
                          name={`_selectedProducts.${index}.discount`}
                          title="Desconto"
                          hideButtons
                          max={100}
                          sx={{ width: '100%' }}
                          slotProps={{
                            inputWrapper: {
                              sx: {
                                backgroundColor: 'transparent',
                                borderLeft: 'none',
                                borderRight: 'none',
                                padding: (theme) => theme.spacing(1),
                              },
                            },
                          }}
                        />
                      </Grid>

                      <Grid item xs={6} md={2}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                            {fCurrency(totalPrice) || 'R$ 0,00'}
                          </Typography>
                          <IconButton size="small" color="error" onClick={() => remove(index)}>
                            <Iconify icon="eva:trash-2-outline" width={16} />
                          </IconButton>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                );
              })}

              <Paper variant="outlined" sx={{ p: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12}>
                    <Typography variant="h6" sx={{ textAlign: 'center' }}>
                      Total:{' '}
                      {fCurrency(
                        watchedProducts.reduce((sum: number, item: OrderItem) => {
                          const unitPrice = item?.unitPrice || 0;
                          const quantity = item?.quantity || 1;
                          const discount = item?.discount || 0;
                          const itemTotal = unitPrice * quantity;
                          const discountValue = (discount / 100) * itemTotal;
                          return sum + (itemTotal - discountValue);
                        }, 0),
                      ) || 'R$ 0,00'}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
            </Stack>
          </CardContent>
        </Card>
      )}

      {fields.length === 0 && (
        <Paper sx={{ p: 4, textAlign: 'center', bgcolor: 'grey.50' }}>
          <Typography variant="body2" color="text.secondary">
            Nenhum produto selecionado ainda. Use o campo de busca acima para encontrar e adicionar
            produtos.
          </Typography>
        </Paper>
      )}
    </Stack>
  );
}
