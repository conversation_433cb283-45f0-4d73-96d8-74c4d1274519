'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
} from '@mui/material';
import { Box } from '@mui/system';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAuthContext } from 'src/contexts/auth/hooks';
import Iconify from 'src/components/iconify';
import { LoadingButton } from '@mui/lab';
import { useBlingContext } from 'src/contexts/integrations/bling/bling-context';
import { useMutation } from '@tanstack/react-query';
import { BlingOrderRepository } from 'src/repositories/integrations/bling/bling-order-repository';
import { useSnackbar } from 'src/components/snackbar';
import FormProvider from 'src/components/hook-form/form-provider';
import { BlingOrderPayloadBuilder, OrderItem } from 'src/utils/bling/order-payload-builder';
import type { IBlingOrderCreate } from 'src/types/integrations/bling/order';
import type { Parcela } from './hooks/use-payment-installments';
import BlingIntegrationSelectStep from './bling-integration-select-step';
import BlingOrderClientStep from './bling-order-client-step';
import BlingAssistedSellingProductStep from './bling-assisted-selling-product-step';
import BlingOrderSummaryStep from './bling-order-summary-step';
import { useBoolean } from 'src/hooks/use-boolean';
import { CreateClient } from './_components/create-client';
import { IBlingClientDetails } from 'src/types/integrations/bling/client';
import { useBlingClientSearch } from './hooks/use-bling-client-search';

type CreateBlingOrderDrawerProps = {
  open: boolean;
  onClose: VoidFunction;
} & DrawerProps;

const STEPS = [
  {
    label: 'Selecione uma integração',
    description: 'Selecione a integração que deseja criar o pedido',
  },
  {
    label: 'Selecione o cliente',
    description: 'Selecione o cliente que deseja usar no pedido',
  },
  {
    label: 'Selecione os produtos',
    description: 'Selecione os produtos que deseja vender',
  },
  {
    label: 'Finalize o pedido',
    description: 'Revise os dados e confirme a criação do pedido',
  },
];

export default function CreateBlingOrderDrawer({
  open,
  onClose,
  ...rest
}: CreateBlingOrderDrawerProps) {
  const { company } = useAuthContext();
  const { blingIntegrations } = useBlingContext();
  const { enqueueSnackbar } = useSnackbar();

  const newClient = useBoolean();

  const [currentStep, setCurrentStep] = useState(0);

  const defaultValues = useMemo(
    () => ({
      _integrationId: null,
      contato: {
        id: 0,
        tipoPessoa: 'F' as 'F' | 'J',
        numeroDocumento: '',
      },
      _selectedProducts: [],
      data: new Date(),
      dataSaida: new Date(),
      dataPrevista: new Date(),
      numeroPedidoCompra: '',
      outrasDespesas: 0,
      desconto: {
        valor: 0,
        unidade: 'REAL' as 'REAL' | 'PERCENTUAL',
      },
      observacoes: '',
      observacoesInternas: '',
      orderDetails: {
        condicoesPagamento: {
          condicao: '',
          categoria: '',
          parcelas: [],
        },
      },
      transporte: {
        fretePorConta: 0,
        frete: 0,
        quantidadeVolumes: 0,
        pesoBruto: 0,
        prazoEntrega: 0,
        transportador: { id: 0, nome: '' },
        etiqueta: {},
      },
    }),
    [],
  );

  const methods = useForm<FormDataProps>({
    // resolver: yupResolver(YupBlingOrderFormSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = methods;

  const watchedIntegrationId = watch('_integrationId');
  const watchedContato = watch('contato');
  const watchedProducts = watch('_selectedProducts');

  const createBlingOrder = useMutation({
    mutationFn: ({
      companyId,
      integrationId,
      pedidoData,
    }: {
      companyId: number;
      integrationId: number;
      pedidoData: IBlingOrderCreate;
    }) => BlingOrderRepository.create(companyId, integrationId, pedidoData),
    onSuccess: () => {
      enqueueSnackbar('Pedido criado com sucesso!', { variant: 'success' });
      onClose();
      reset();
    },
    onError: (error: any) => {
      console.error('Erro ao criar pedido:', error);
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        'Erro ao criar pedido. Verifique os dados e tente novamente.';

      enqueueSnackbar(errorMessage, { variant: 'error' });
    },
  });

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceed = (step: number): boolean => {
    switch (step) {
      case 0:
        return !!watchedIntegrationId;
      case 1:
        return (watchedContato?.id || 0) > 0;
      case 2:
        return (watchedProducts?.length || 0) > 0;
      case 3:
        return true;
      default:
        return false;
    }
  };

  const handleSetIntegration = (integrationId: number) => {
    setValue('_integrationId', integrationId);
  };

  const { setSelectedClient } = useBlingClientSearch(watchedIntegrationId!, watchedContato.id);

  const handleClientSelect = (client: IBlingClientDetails) => {
    setValue('contato.id', client.data.id);
    setValue('contato.numeroDocumento', client.data.numeroDocumento);
    setValue('contato.tipoPessoa', client.data.tipo === 'F' ? 'F' : 'J');

    setSelectedClient(client);
  };

  const onSubmit = handleSubmit(async (data) => {
    if (!data._integrationId || !company) {
      enqueueSnackbar('Dados incompletos para criação do pedido', { variant: 'error' });
      return;
    }

    try {
      const formatDate = (v: string | Date | undefined) =>
        typeof v === 'string' ? v : v ? new Date(v).toISOString().split('T')[0] : undefined;

      const normalizedParcelas = (
        (data.orderDetails?.condicoesPagamento?.parcelas || []) as Parcela[]
      ).map((parcela) => ({
        ...parcela,
        data:
          typeof parcela.data === 'string'
            ? parcela.data
            : new Date(parcela.data).toISOString().split('T')[0],
      }));

      const clientData = {
        data: {
          id: data.contato.id,
          nome: 'Cliente Selecionado', // Será preenchido pelos dados reais
          tipo: data.contato.tipoPessoa,
          numeroDocumento: data.contato.numeroDocumento,
          endereco: { geral: {} },
          financeiro: { categoria: { id: 0 } },
        },
      };

      const payload = BlingOrderPayloadBuilder.build({
        client: clientData as IBlingClientDetails,
        items: data._selectedProducts || [],
        orderDetails: {
          data: formatDate(data.data),
          dataSaida: formatDate(data.dataSaida),
          dataPrevista: formatDate(data.dataPrevista),
          numeroPedidoCompra: data.numeroPedidoCompra,
          outrasDespesas: data.outrasDespesas,
          observacoes: data.observacoes,
          observacoesInternas: data.observacoesInternas,
          condicoesPagamento: {
            ...data.orderDetails?.condicoesPagamento,
            parcelas: normalizedParcelas,
          },
        },
        desconto: data.desconto,
        transporte: data.transporte,
      });

      await createBlingOrder.mutateAsync({
        companyId: company.id,
        integrationId: data._integrationId,
        pedidoData: payload,
      });
    } catch (error) {
      console.error('Erro ao submeter pedido:', error);
    }
  });

  const onCloseDrawerOverride = (event: {}, reason: 'backdropClick' | 'escapeKeyDown') => {
    if (reason === 'backdropClick' || reason === 'escapeKeyDown') {
      return;
    }
    onClose();
  };

  useEffect(() => {
    if (blingIntegrations.length === 1 && !watchedIntegrationId) {
      setValue('_integrationId', blingIntegrations[0].id);
      setCurrentStep(1);
    }
  }, [blingIntegrations, watchedIntegrationId, setValue]);

  useEffect(() => {
    if (open && blingIntegrations.length === 1) {
      setValue('_integrationId', blingIntegrations[0].id);
      setCurrentStep(1);
    }
  }, [open, blingIntegrations, setValue]);

  const renderStepContent = (stepIndex: number) => {
    switch (stepIndex) {
      case 0:
        return (
          <BlingIntegrationSelectStep
            integrations={blingIntegrations}
            selectedId={watchedIntegrationId || null}
            onSelect={handleSetIntegration}
          />
        );

      case 1:
        return watchedIntegrationId ? (
          <BlingOrderClientStep integrationId={watchedIntegrationId} newClient={newClient} />
        ) : null;

      case 2:
        return watchedIntegrationId ? (
          <BlingAssistedSellingProductStep integrationId={watchedIntegrationId} />
        ) : null;

      case 3:
        return (watchedContato?.id || 0) > 0 ? <BlingOrderSummaryStep /> : null;

      default:
        return null;
    }
  };

  const getStepActions = (stepIndex: number) => {
    const isLastStep = stepIndex === STEPS.length - 1;
    const canGoNext = canProceed(stepIndex);

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', mt: 2, gap: 1 }}>
        {stepIndex > 0 && (
          <Button onClick={previousStep} disabled={createBlingOrder.isLoading}>
            Voltar
          </Button>
        )}
        <Box sx={{ flex: '1 1 auto' }} />

        {!isLastStep ? (
          <Button
            variant="contained"
            onClick={nextStep}
            disabled={!canGoNext || createBlingOrder.isLoading}
          >
            Próximo
          </Button>
        ) : (
          <LoadingButton
            variant="contained"
            loading={createBlingOrder.isLoading}
            type="submit"
            disabled={!canGoNext}
          >
            Criar Pedido
          </LoadingButton>
        )}
      </Box>
    );
  };

  return (
    <Drawer
      open={open}
      onClose={onCloseDrawerOverride}
      variant="temporary"
      anchor="right"
      sx={{
        '& .MuiDrawer-paper': {
          width: { xs: '100%', md: 1000 },
          maxWidth: '100%',
        },
      }}
      {...rest}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <Stack spacing={2} sx={{ p: 3, height: '100%', overflow: 'auto' }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="h4">Criar pedido no Bling</Typography>
            <IconButton onClick={onClose} disabled={createBlingOrder.isLoading}>
              <Iconify icon="material-symbols:close" width={24} />
            </IconButton>
          </Stack>

          <Box sx={{ mb: 2 }}>
            <Typography variant="caption" color="text.secondary">
              Passo {currentStep + 1} de {STEPS.length}
            </Typography>
          </Box>

          <Stepper activeStep={currentStep} orientation="vertical">
            {STEPS.map((step, index) => (
              <Step key={step.label}>
                <StepLabel>
                  <Typography variant="subtitle1">{step.label}</Typography>
                </StepLabel>

                <StepContent>
                  <Stack spacing={2}>
                    <Typography variant="body2" color="text.secondary">
                      {step.description}
                    </Typography>

                    {renderStepContent(index)}
                    {getStepActions(index)}
                  </Stack>
                </StepContent>
              </Step>
            ))}
          </Stepper>

          {createBlingOrder.isLoading && (
            <Alert severity="info">Criando pedido no Bling... Por favor, aguarde.</Alert>
          )}

          {Object.keys(errors).length > 0 && (
            <Alert severity="error">
              <Typography variant="subtitle2">Corrija os seguintes erros:</Typography>
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field}>{error?.message}</li>
                ))}
              </ul>
            </Alert>
          )}
        </Stack>
      </FormProvider>

      <CreateClient
        newClient={newClient}
        integrationId={watchedIntegrationId || 0}
        handleClientSelect={handleClientSelect}
      />
    </Drawer>
  );
}

type FormDataProps = {
  _integrationId: number | null;
  contato: {
    id: number;
    tipoPessoa: 'F' | 'J';
    numeroDocumento: string;
  };
  _selectedProducts: OrderItem[];
  data: string | Date;
  dataSaida: string | Date;
  dataPrevista: string | Date;
  numeroPedidoCompra: string;
  outrasDespesas: number;
  desconto: {
    valor: number;
    unidade: 'REAL' | 'PERCENTUAL';
  };
  observacoes: string;
  observacoesInternas: string;
  orderDetails: {
    condicoesPagamento: {
      condicao: string;
      categoria: string;
      parcelas: Array<{
        id: string;
        dias: number;
        data: string | Date;
        valor: number;
        formaPagamento: any;
        observacao?: string;
      }>;
    };
  };
  transporte: {
    fretePorConta: number;
    frete: number;
    quantidadeVolumes: number;
    pesoBruto: number;
    prazoEntrega: number;
    transportador: { id: number; nome: string };
    etiqueta: Record<string, any>;
  };
};
