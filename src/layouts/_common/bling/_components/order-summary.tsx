'use client';

import { Box, Grid, Stack, Typography } from '@mui/material';
import { fCurrency } from 'src/utils/format-number';
import type { OrderItem } from 'src/utils/bling/order-payload-builder';

interface OrderSummaryProps {
  selectedProducts: OrderItem[];
  totalQuantity: number;
  subtotal: number;
  finalTotal: number;
  outrasDespesas: number;
  frete: number;
  descontoGlobal: number;
  descontoUnidade: string;
  descontoGlobalCalculado: number;
}

interface SummaryItemProps {
  title: string;
  value: string | number;
  color?: string;
}

function SummaryItem({ title, value, color = 'primary' }: SummaryItemProps) {
  return (
    <Box sx={{ textAlign: 'center' }}>
      <Typography variant="h4" color={`${color}.main`}>
        {value}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {title}
      </Typography>
    </Box>
  );
}

interface DetailRowProps {
  label: string;
  value: string;
  color?: string;
}

function DetailRow({ label, value, color }: DetailRowProps) {
  return (
    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
      <Typography variant="body2" color={color}>
        {label}
      </Typography>
      <Typography variant="body2" color={color}>
        {value}
      </Typography>
    </Box>
  );
}

export default function OrderSummary({
  selectedProducts,
  totalQuantity,
  subtotal,
  finalTotal,
  outrasDespesas,
  frete,
  descontoGlobal,
  descontoUnidade,
  descontoGlobalCalculado,
}: OrderSummaryProps) {
  const hasAdditionalCharges =
    Number(outrasDespesas) > 0 || Number(descontoGlobal) > 0 || Number(frete) > 0;

  return (
    <Grid container spacing={2}>
      <Grid item xs={12} md={3}>
        <SummaryItem
          title={selectedProducts.length === 1 ? 'Produto' : 'Produtos'}
          value={selectedProducts.length}
        />
      </Grid>

      <Grid item xs={12} md={3}>
        <SummaryItem title="Quantidade Total" value={totalQuantity} />
      </Grid>

      <Grid item xs={12} md={3}>
        <SummaryItem title="Subtotal" value={fCurrency(subtotal)} color="info" />
      </Grid>

      <Grid item xs={12} md={3}>
        <SummaryItem title="Valor Final" value={fCurrency(finalTotal)} />
      </Grid>

      {hasAdditionalCharges && (
        <Grid item xs={12}>
          <Box
            sx={{
              mt: 2,
              p: 2,
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'divider',
            }}
          >
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Detalhamento:
            </Typography>
            <Stack spacing={1}>
              <DetailRow label="Subtotal dos produtos:" value={fCurrency(subtotal)} />

              {Number(outrasDespesas) > 0 && (
                <DetailRow
                  label="+ Outras despesas:"
                  value={fCurrency(Number(outrasDespesas))}
                  color="info.main"
                />
              )}

              {Number(frete) > 0 && (
                <DetailRow label="+ Frete:" value={fCurrency(Number(frete))} color="info.main" />
              )}

              {Number(descontoGlobal) > 0 && (
                <DetailRow
                  label={`- Desconto (${
                    descontoUnidade === 'PERCENTUAL' ? `${descontoGlobal}%` : 'R$'
                  }):`}
                  value={fCurrency(descontoGlobalCalculado)}
                  color="error.main"
                />
              )}

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  pt: 1,
                  borderTop: '1px solid',
                  borderColor: 'divider',
                }}
              >
                <Typography variant="subtitle2">Total Final:</Typography>
                <Typography variant="subtitle2" color="primary">
                  {fCurrency(finalTotal)}
                </Typography>
              </Box>
            </Stack>
          </Box>
        </Grid>
      )}
    </Grid>
  );
}
