import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import Iconify from 'src/components/iconify';
import { fCurrency } from 'src/utils/format-number';
import { usePaymentInstallments } from 'src/layouts/_common/bling/hooks/use-payment-installments';
import { useDebounce } from 'src/hooks/use-debounce';
import { RHFTextField, RHFDatePicker, RHFAutocomplete } from 'src/components/hook-form';
import { useQuery } from '@tanstack/react-query';
import { useAuthContext } from 'src/contexts/auth/hooks';
import { BlingPaymentMethodRepository } from 'src/repositories/integrations/bling/bling-payment-method-repository';
import RHFNumericField from 'src/components/hook-form/rhf-numericField';
import { IBlingPaymentMethod } from 'src/types/integrations/bling/payment-method';

interface PaymentSectionProps {
  totalValue: number;
  baseDate: Date;
  integrationId: number;
}

export function PaymentSection({ totalValue, baseDate, integrationId }: PaymentSectionProps) {
  const { company } = useAuthContext();
  const { watch } = useFormContext();
  const [searchValues, setSearchValues] = useState<{ [key: number]: string }>({});
  const condicaoPagamento: string = watch('orderDetails.condicoesPagamento.condicao') || '';
  const {
    parcelas,
    addParcela,
    removeParcela,
    updateParcela,
    generateParcelas,
    recalculateParcelas,
    totalParcelas,
  } = usePaymentInstallments();

  const debouncedTotal = useDebounce<number>(totalValue, 250);

  const { data: paymentMethods = null, isLoading } = useQuery({
    queryKey: ['bling-payment-methods', company?.id, integrationId],
    queryFn: () =>
      BlingPaymentMethodRepository.getPaymentMethods({
        params: {
          companyId: company!.id,
          integrationId,
        },
      }),
    enabled: !!company?.id && !!integrationId,
    keepPreviousData: true,
  });

  useEffect(() => {
    if (parcelas.length > 0 && debouncedTotal > 0) {
      recalculateParcelas(debouncedTotal);
    }
  }, [debouncedTotal, parcelas.length, recalculateParcelas]);

  const handleGenerateParcelas = () => {
    generateParcelas(condicaoPagamento, totalValue, baseDate);
  };

  const getFilteredPaymentMethods = (searchText: string): IBlingPaymentMethod[] => {
    if (!paymentMethods) return [];

    if (!searchText.trim()) {
      return paymentMethods;
    }

    return paymentMethods.filter((pm) =>
      pm.data.descricao.toLowerCase().includes(searchText.toLowerCase()),
    );
  };

  const handleSearchChange = (index: number, value: string) => {
    setSearchValues((prev) => ({
      ...prev,
      [index]: value,
    }));
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Pagamento
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
          <RHFTextField
            name="orderDetails.condicoesPagamento.condicao"
            fullWidth
            label="Condição de pagamento"
            placeholder="Ex: 160 30 60"
            helperText="Digite os dias separados por espaço (ex: 160 30 60)"
            sx={{ flex: 1 }}
          />
          <Button
            variant="contained"
            color="primary"
            onClick={handleGenerateParcelas}
            sx={{ height: 56, minWidth: 120 }}
          >
            Gerar parcelas
          </Button>
        </Box>

        {parcelas.length > 0 ? (
          <Box>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Parcelas ({parcelas.length})
            </Typography>

            <TableContainer
              component={Paper}
              variant="outlined"
              sx={{ overflowX: 'auto', width: '100%' }}
            >
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell style={{ minWidth: 20 }}>#</TableCell>
                    <TableCell style={{ minWidth: 100 }}>Dias</TableCell>
                    <TableCell style={{ minWidth: 170 }}>Data</TableCell>
                    <TableCell style={{ minWidth: 200 }}>Valor</TableCell>
                    <TableCell style={{ minWidth: 250 }}>Forma</TableCell>
                    <TableCell>Ações</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {parcelas.map((parcela, index) => (
                    <TableRow key={parcela.id}>
                      <TableCell>
                        <Typography variant="body2">{index + 1}</Typography>
                      </TableCell>
                      <TableCell>
                        <RHFTextField
                          name={`orderDetails.condicoesPagamento.parcelas.${index}.dias`}
                          onBlur={(e) => {
                            const dias = parseInt(e.target.value) || 0;
                            updateParcela(index, 'dias', dias, baseDate);
                          }}
                          inputProps={{ min: 0 }}
                          fullWidth
                        />
                      </TableCell>
                      <TableCell>
                        <RHFDatePicker
                          name={`orderDetails.condicoesPagamento.parcelas.${index}.data`}
                          slotProps={{
                            textField: {
                              size: 'small',
                              fullWidth: true,
                            },
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <RHFNumericField
                          name={`orderDetails.condicoesPagamento.parcelas.${index}.valor`}
                          label=""
                          min={0}
                          step={0.01}
                          InputProps={{
                            startAdornment: null,
                          }}
                          fullWidth
                        />
                      </TableCell>
                      <TableCell>
                        <RHFAutocomplete
                          name={`orderDetails.condicoesPagamento.parcelas.${index}.formaPagamento`}
                          placeholder="Pesquise ou selecione uma forma de pagamento"
                          options={getFilteredPaymentMethods(searchValues[index] || '')}
                          getOptionLabel={(option) => option?.data?.descricao || ''}
                          isOptionEqualToValue={(option, value) =>
                            option?.data?.id === value?.data?.id
                          }
                          onInputChange={(event, value) => handleSearchChange(index, value)}
                          loading={isLoading}
                          freeSolo={false}
                          filterOptions={(options) => options}
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton size="small" color="error" onClick={() => removeParcela(index)}>
                          <Iconify icon="eva:trash-2-outline" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Box
              sx={{
                mt: 2,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Button
                variant="outlined"
                startIcon={<Iconify icon="eva:plus-fill" />}
                onClick={addParcela}
              >
                Adicionar outra parcela
              </Button>

              <Typography variant="body2" color="text.secondary">
                Total das parcelas: {fCurrency(totalParcelas)}
              </Typography>
            </Box>
          </Box>
        ) : (
          <Box sx={{ textAlign: 'center', py: 3, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Configure as condições de pagamento acima e clique em &quot;Gerar parcelas&quot;
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}
