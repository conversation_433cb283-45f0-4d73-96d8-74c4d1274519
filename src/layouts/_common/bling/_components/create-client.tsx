'use client';

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  Card,
  CardContent,
  Collapse,
  Drawer,
  DrawerProps,
  Grid,
  MenuItem,
  Stack,
  Typography,
} from '@mui/material';
import * as Yup from 'yup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { enqueueSnackbar } from 'notistack';
import { useForm } from 'react-hook-form';
import FormProvider, { RHFTextField, RHFSelect } from 'src/components/hook-form';
import { UseBooleanReturnType } from 'src/hooks/use-boolean';
import { BlingClientRepository } from 'src/repositories/integrations/bling/bling-client-repository';
import { IBlingClientDetails, ICreateBlingClient } from 'src/types/integrations/bling/client';
import { AppError } from 'src/utils/AppError';
import { useAuthContext } from 'src/contexts/auth/hooks';

type CreateClientProps = {
  newClient: UseBooleanReturnType;
  handleClientSelect: (client: IBlingClientDetails) => void;
  integrationId: number;
} & DrawerProps;

export function CreateClient({
  newClient,
  integrationId,
  handleClientSelect,
  ...rest
}: CreateClientProps) {
  const { company } = useAuthContext();

  const queryClient = useQueryClient();

  const newClientForm = useForm<NewClientFormData>({
    resolver: yupResolver(newClientSchema),
    defaultValues: {
      nome: '',
      numeroDocumento: '',
      tipo: 'F',
      celular: '',
      endereco: {
        geral: {
          endereco: '',
          cep: '',
          bairro: '',
          municipio: '',
          uf: '',
          numero: '',
          complemento: '',
        },
      },
    },
  });

  const { handleSubmit } = newClientForm;

  const createClientMutation = useMutation({
    mutationFn: (clientData: ICreateBlingClient) =>
      BlingClientRepository.create(company!.id, integrationId, clientData),
  });

  const onSubmit = handleSubmit((data) => {
    const clientData: ICreateBlingClient = {
      ...data,
      situacao: 'A',
      endereco: {
        geral: {
          ...data.endereco.geral,
          complemento: data.endereco.geral.complemento || '',
        },
      },
    };

    createClientMutation.mutate(clientData, {
      onSuccess: async (newClientData) => {
        handleClientSelect(newClientData);
        enqueueSnackbar('Cliente/contato criado com sucesso!');
        newClientForm.reset();
        newClient.onFalse();

        queryClient.invalidateQueries(['bling-clients-search']);
        queryClient.invalidateQueries(['bling-clients-details']);
      },
      onError: (error) => {
        const isAppError = error instanceof AppError;
        const title = isAppError
          ? error.message
          : 'Não foi possível criar cliente.\nTente novamente mais tarde';

        enqueueSnackbar(title, { variant: 'error' });
      },
    });
  });

  return (
    <Drawer
      open={newClient.value}
      onClose={newClient.onFalse}
      variant="temporary"
      anchor="left"
      sx={{
        '& .MuiDrawer-paper': {
          width: { xs: '100%', md: 700 },
          maxWidth: '100%',
        },
      }}
      {...rest}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-start',
          minHeight: '100vh',
          p: 2,
        }}
      >
        <Card sx={{ width: '100%', maxWidth: 600 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Criar Novo Cliente
            </Typography>

            <FormProvider methods={newClientForm} onSubmit={onSubmit}>
              <Stack spacing={3}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <RHFTextField fullWidth label="Nome *" name="nome" />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <RHFTextField fullWidth label="Documento (CPF/CNPJ) *" name="numeroDocumento" />
                  </Grid>
                </Grid>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <RHFSelect fullWidth label="Tipo *" name="tipo">
                      <MenuItem value="F" selected>
                        Pessoa Física
                      </MenuItem>
                      <MenuItem value="J">Pessoa Jurídica</MenuItem>
                    </RHFSelect>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <RHFTextField fullWidth label="Celular *" name="celular" />
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>
                  Endereço
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={8}>
                    <RHFTextField fullWidth label="Endereço *" name="endereco.geral.endereco" />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <RHFTextField fullWidth label="Número *" name="endereco.geral.numero" />
                  </Grid>
                </Grid>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <RHFTextField fullWidth label="CEP *" name="endereco.geral.cep" />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <RHFTextField fullWidth label="Bairro *" name="endereco.geral.bairro" />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <RHFTextField fullWidth label="Complemento" name="endereco.geral.complemento" />
                  </Grid>
                </Grid>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={8}>
                    <RHFTextField fullWidth label="Município *" name="endereco.geral.municipio" />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <RHFTextField
                      fullWidth
                      label="UF *"
                      name="endereco.geral.uf"
                      inputProps={{ maxLength: 2, style: { textTransform: 'uppercase' } }}
                    />
                  </Grid>
                </Grid>

                <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
                  <LoadingButton
                    type="submit"
                    variant="contained"
                    loading={createClientMutation.isLoading}
                  >
                    Criar Cliente
                  </LoadingButton>
                  <Button variant="outlined" onClick={newClient.onFalse}>
                    Cancelar
                  </Button>
                </Stack>
              </Stack>
            </FormProvider>
          </CardContent>
        </Card>
      </Box>
    </Drawer>
  );
}

const newClientSchema = Yup.object({
  nome: Yup.string().required('Nome é obrigatório').min(2, 'Nome deve ter pelo menos 2 caracteres'),
  numeroDocumento: Yup.string().required('Documento é obrigatório'),
  tipo: Yup.string()
    .required('Tipo é obrigatório')
    .oneOf(['F', 'J'], 'Tipo deve ser F (Física) ou J (Jurídica)'),
  celular: Yup.string().required('Celular é obrigatório'),
  endereco: Yup.object({
    geral: Yup.object({
      endereco: Yup.string().required('Endereço é obrigatório'),
      cep: Yup.string().required('CEP é obrigatório'),
      bairro: Yup.string().required('Bairro é obrigatório'),
      municipio: Yup.string().required('Município é obrigatório'),
      uf: Yup.string().required('UF é obrigatório').length(2, 'UF deve ter 2 caracteres'),
      numero: Yup.string().required('Número é obrigatório'),
      complemento: Yup.string().optional(),
    }),
  }),
});

type NewClientFormData = Yup.InferType<typeof newClientSchema>;
