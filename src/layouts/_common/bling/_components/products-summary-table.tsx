import {
  Card,
  CardContent,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { fCurrency } from 'src/utils/format-number';
import { useOrderCalculations } from 'src/layouts/_common/bling/hooks/use-order-calculations';

import type { OrderItem } from 'src/utils/bling/order-payload-builder';

interface ProductsSummaryTableProps {
  products: OrderItem[];
}

export function ProductsSummaryTable({ products }: ProductsSummaryTableProps) {
  const { totalValue, itemTotals } = useOrderCalculations(products);

  if (products.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Produtos Selecionados (0 itens)
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Nenhum produto selecionado
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Produtos Selecionados ({products.length} {products.length === 1 ? 'item' : 'itens'})
        </Typography>

        <TableContainer component={Paper} variant="outlined">
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Produto</TableCell>
                <TableCell align="center">Qtd</TableCell>
                <TableCell align="right">Preço Unit.</TableCell>
                <TableCell align="right">Desconto %</TableCell>
                <TableCell align="right">Total</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {products.map((item: OrderItem, index: number) => {
                const { finalTotal } = itemTotals[index];

                return (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography variant="body2">
                        {item.product?.data?.nome || 'Produto'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Código: {item.product?.data?.codigo}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">{item.quantity}</TableCell>
                    <TableCell align="right">{fCurrency(item.unitPrice || 0)}</TableCell>
                    <TableCell align="right">{item.discount || 0}%</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                      {fCurrency(finalTotal)}
                    </TableCell>
                  </TableRow>
                );
              })}
              <TableRow>
                <TableCell colSpan={4}>
                  <Typography variant="subtitle2">Total Geral</Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="h6" color="primary">
                    {fCurrency(totalValue)}
                  </Typography>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );
}
