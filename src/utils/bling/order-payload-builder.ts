import { IBlingOrderCreate, ItemPedidoDTO } from 'src/types/integrations/bling/order';
import { IBlingClientDetails } from 'src/types/integrations/bling/client';
import { IBlingProduct } from 'src/types/integrations/bling/product';

export interface OrderItem {
  product: IBlingProduct;
  quantity: number;
  unitPrice?: number;
  discount?: number;
  description?: string;
}

export interface OrderPayloadParams {
  client: IBlingClientDetails;
  items: OrderItem[];
  orderDetails?: {
    numero?: number;
    numeroLoja?: string;
    data?: string;
    dataSaida?: string;
    dataPrevista?: string;
    numeroPedidoCompra?: string;
    outrasDespesas?: number;
    observacoes?: string;
    observacoesInternas?: string;
    // Condições de pagamento
    condicoesPagamento?: {
      condicao?: string;
      categoria?: string;
      parcelas?: Array<{
        id: string;
        dias: number;
        data: string;
        valor: number;
        formaPagamento: {
          data: {
            id: number;
            descricao: string;
          };
        } | null;
        observacao?: string;
      }>;
    };
  };
  situacao?: {
    id: number;
    valor: number;
  };
  loja?: {
    id: number;
  };
  desconto?: {
    valor: number;
    unidade: 'REAL' | 'PERCENTUAL';
  };
  vendedor?: {
    id: number;
  };
  transporte?: {
    fretePorConta?: number;
    frete?: number;
    quantidadeVolumes?: number;
    pesoBruto?: number;
    prazoEntrega?: number;
    transportador?: {
      id: number;
      nome: string;
    };
    etiqueta?: {
      nome?: string;
      endereco?: string;
      numero?: string;
      complemento?: string;
      municipio?: string;
      uf?: string;
      cep?: string;
      bairro?: string;
      nomePais?: string;
    };
  };
}

export class BlingOrderPayloadBuilder {
  static build(params: OrderPayloadParams): IBlingOrderCreate {
    const {
      client,
      items,
      orderDetails = {},
      situacao = { id: 0, valor: 0 },
      loja = { id: 0 },
      desconto = { valor: 0, unidade: 'REAL' as const },
      vendedor = { id: 0 },
      transporte = {},
    } = params;

    // Data atual se não fornecida
    const dataAtual = new Date().toISOString().split('T')[0];

    // Construir itens do pedido
    const itensFormatados: Omit<ItemPedidoDTO, 'id'>[] = items.map((item, index) => ({
      codigo: item.product.data?.codigo || `ITEM-${index + 1}`,
      unidade: item.product.data?.unidade || 'UN',
      quantidade: item.quantity,
      desconto: item.discount || 0,
      valor: item.unitPrice || item.product.data?.preco || 0,
      aliquotaIPI: 0,
      descricao: item.description || item.product.data?.nome || '',
      descricaoDetalhada: item.product.data?.descricaoCurta || '',
      produto: {
        id: item.product.data?.id || 0,
      },
      comissao: {
        base: 0,
        aliquota: 0,
        valor: 0,
      },
    }));

    // Construir transporte
    const transporteFormatado = {
      fretePorConta: transporte.fretePorConta || 0,
      frete: transporte.frete || 0,
      quantidadeVolumes: transporte.quantidadeVolumes || itensFormatados.length,
      pesoBruto: transporte.pesoBruto || this.calculateTotalWeight(items),
      prazoEntrega: transporte.prazoEntrega || 0,
      contato: {
        id: transporte.transportador?.id || 0,
        nome: transporte.transportador?.nome || '',
      },
      etiqueta: transporte.etiqueta
        ? {
            nome: transporte.etiqueta?.nome || '',
            endereco: transporte.etiqueta?.endereco || '',
            numero: transporte.etiqueta?.numero || '',
            complemento: transporte.etiqueta?.complemento || '',
            municipio: transporte.etiqueta?.municipio || '',
            uf: transporte.etiqueta?.uf || '',
            cep: transporte.etiqueta?.cep || '',
            bairro: transporte.etiqueta?.bairro || '',
            nomePais: transporte.etiqueta?.nomePais || '',
          }
        : undefined,
      volumes: [],
    };

    const payload: IBlingOrderCreate = {
      numero: 0,
      numeroLoja: '',
      data: orderDetails.data || dataAtual,
      dataSaida: orderDetails.dataSaida || dataAtual,
      dataPrevista: orderDetails.dataPrevista || dataAtual,
      contato: {
        id: client.data.id,
        tipoPessoa: client.data.tipo === 'F' ? 'F' : 'J',
        numeroDocumento: client.data.numeroDocumento,
      },
      situacao,
      loja,
      numeroPedidoCompra: orderDetails.numeroPedidoCompra || '',
      outrasDespesas: orderDetails.outrasDespesas || 0,
      observacoes: orderDetails.observacoes || '',
      observacoesInternas: orderDetails.observacoesInternas || '',
      desconto,
      categoria: {
        id: client.data.financeiro?.categoria?.id || 0,
      },
      notaFiscal: {
        id: 0,
      },
      tributacao: {
        totalICMS: 0,
        totalIPI: 0,
      },
      itens: itensFormatados,
      parcelas:
        orderDetails.condicoesPagamento?.parcelas &&
        orderDetails.condicoesPagamento.parcelas.length > 0
          ? orderDetails.condicoesPagamento.parcelas.map((parcela, index) => ({
              id: index,
              dataVencimento: parcela.data,
              valor: parcela.valor,
              observacoes: parcela.observacao || '',
              formaPagamento: {
                id: parcela.formaPagamento?.data?.id || 0,
              },
            }))
          : [
              {
                id: 0,
                dataVencimento: orderDetails.dataPrevista || dataAtual,
                valor: this.calculateTotalValue(itensFormatados),
                observacoes: '',
                formaPagamento: {
                  id: 0,
                },
              },
            ],
      transporte: transporteFormatado,
      vendedor,
      intermediador: {
        cnpj: '',
        nomeUsuario: '',
      },
      taxas: {
        taxaComissao: 0,
        custoFrete: transporte.frete || 0,
        valorBase: this.calculateTotalValue(itensFormatados),
      },
    };

    return payload;
  }

  private static calculateTotalWeight(items: OrderItem[]): number {
    return items.reduce((total, item) => {
      const weight = item.product.data?.pesoBruto || 0;
      return total + weight * item.quantity;
    }, 0);
  }

  private static calculateTotalValue(items: Omit<ItemPedidoDTO, 'id'>[]): number {
    return items.reduce((total, item) => {
      const itemTotal = item.valor * item.quantidade;
      const discountValue = (itemTotal * item.desconto) / 100;
      return total + (itemTotal - discountValue);
    }, 0);
  }
}
